<!-- ConversationView.svelte -->
<script lang="ts">
	import { onMount, onDestroy, createEventDispatcher } from 'svelte';
	import ConversationHeader from './ConversationHeader.svelte';
	import MessageList from './MessageList.svelte';
	import MessageInput from './MessageInput.svelte';
	import { conversationStore } from '$lib/stores/conversationStore';
	import { conversationService } from '$lib/services/conversationService';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import type { Message } from '$lib/types/customer';
	import { services } from '$src/lib/api/features';
	import { CustomerService } from '$lib/api/features/customer/customers.service';
	import { selectedTicketData, ticketStore } from '$lib/stores/ticketStore';
	import { tabCacheStore } from '$lib/stores/tabCacheStore';

	import { page } from '$app/stores';

	// Initialize customer service
	const customerService = new CustomerService();

	// Event dispatcher for parent component communication
	const dispatch = createEventDispatcher();

	export let customerId: number;
	export let platformId: number;
	export let ticketId: number | null = null;
	// ticketIds prop removed - no longer needed with centralized store
	export let users: any[] = [];
	export let priorities: any[] = [];
	export let statuses: any[] = [];
	export let topics: any[] = [];
	export let access_token: string = '';
	// Removed latest_ticket_owner_id and currentLoginUser props since we now use local variables from polling
	// export let latest_ticket_owner_id: number;
	// export let currentLoginUser: any[] = [];
	export let focusedTicketId: number | null = null;
	export let showMessageInput: boolean = true; // Default to true for backward compatibility
	export let isReadOnly: boolean = false;
	export let activeTab: string = ''; // Current tab context for automatic message marking

	let messages: Message[] = [];
	let loading = true;
	let connected = false;
	let customerName = '';
	let channelName = '';
	let previousPlatformId: number | null = null;
	let previousCustomerId: number | null = null;

	let loadingMore = false;
	let hasMore = false;
	let error: string | null = null;

	// Track if we need to retry automatic message marking after ticket data loads
	let pendingAutoMarkRead: { platformId: number; customerId: number } | null = null;

	// Ticket data from centralized store
	$: ticketData = $selectedTicketData;
	$: ticket = ticketData?.ticket || null;
	$: ticketLoading = ticketData?.loading || false;
	$: ticketError = ticketData?.error || null;
	$: loginUser = ticketData?.loginUser || $page.data.id;

	// Use local variables that are updated by polling instead of props
	// ticket.owner_id contains the current ticket owner ID
	$: canSendMessage = (ticket?.owner_id === loginUser) && (ticket?.status.toLowerCase() !== 'pending_to_close') && (ticket?.status.toLowerCase() !== 'closed');
	$: canMarkMessagesAsRead = (ticket?.owner_id === loginUser) && (activeTab === 'my-assigned' || activeTab === 'others-assigned');
	$: messageInputDisabled = loading || !canSendMessage;

	// Debug logging for automatic message marking conditions
	$: if (ticket || loginUser || activeTab) {
		console.log('ConversationView: Reactive conditions updated:', {
			ticketOwnerId: ticket?.owner_id,
			loginUserId: loginUser,
			activeTab,
			canMarkMessagesAsRead,
			canSendMessage,
			ticketStatus: ticket?.status,
			pendingAutoMarkRead,
			timestamp: new Date().toISOString()
		});
	}

	// Reactive statement to handle automatic message marking when ticket data becomes available
	$: if (pendingAutoMarkRead && canMarkMessagesAsRead && ticket?.owner_id === loginUser) {
		console.log('ConversationView: Ticket data now available, attempting delayed automatic message marking:', {
			pendingAutoMarkRead,
			canMarkMessagesAsRead,
			ticketOwnerId: ticket?.owner_id,
			loginUser
		});

		// Perform the automatic message marking
		handleDelayedAutoMarkRead(pendingAutoMarkRead.platformId, pendingAutoMarkRead.customerId);

		// Clear the pending request
		pendingAutoMarkRead = null;
	}

	// Debug logging to track the values used in canSendMessage calculation
	// $: if (ticket || loginUser) {
	// 	console.log('ConversationView.svelte: canSendMessage calculation:', {
	// 		ticketOwnerId: ticket?.owner_id,
	// 		loginUserId: loginUser,
	// 		canSendMessage,
	// 		messageInputDisabled,
	// 		timestamp: new Date().toISOString()
	// 	});
	// }

	// Subscribe to conversation store
	$: platformData = $conversationStore;
	$: messages = platformData.messages.get(platformId) || [];
	$: hasMore = platformData.hasMore.get(platformId) || false;
	$: loadingMore = platformData.loadingStates.get(platformId) || false;

	let abortController: AbortController | null = null;
	let isDestroyed = false;

	// React to platformId or customerId changes
	$: if (platformId && customerId && (platformId !== previousPlatformId || customerId !== previousCustomerId)) {
		console.log('ConversationView: Platform or customer changed, loading conversation:', {
			platformId,
			customerId,
			previousPlatformId,
			previousCustomerId,
			activeTab,
			isDestroyed
		});

		// Cancel previous operations
		if (abortController) {
			abortController.abort();
		}

		previousPlatformId = platformId;
		previousCustomerId = customerId;

		if (!isDestroyed) {
			loadConversationForPlatform(customerId, platformId);
		}
	}

	// Reactive mechanism to automatically mark messages as read when ticket ownership changes to current user
	// $: if (ticket?.owner_id !== undefined && ticket.owner_id !== previousOwnerId) {
	// 	// Check if ownership changed TO the current user (not away from them)
	// 	if (ticket.owner_id === loginUser && previousOwnerId !== null) {
	// 		// console.log('ConversationView: Ticket ownership changed to current user, auto-marking messages as read:', {
	// 		// 	previousOwnerId,
	// 		// 	newOwnerId: ticket.owner_id,
	// 		// 	loginUser,
	// 		// 	canSendMessage,
	// 		// 	timestamp: new Date().toISOString()
	// 		// });

	// 		// Get unread messages from customers and mark them as read
	// 		// Follow the same validation pattern as existing code
	// 		const unreadMessageIds = messages
	// 			.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
	// 			.map((msg: Message) => msg.id);

	// 		if (canMarkMessagesAsRead && unreadMessageIds.length > 0) {
	// 			// console.log('ConversationView: Auto-marking unread customer messages as read:', unreadMessageIds);
	// 			markMessagesAsRead(unreadMessageIds);
	// 		}

	// 		// For remaining unread messages from System, mark them as read
	// 		if (canMarkMessagesAsRead) {
	// 			// console.log('ConversationView: Auto-marking all system messages as read');
	// 			markAllMessagesAsRead();
	// 		}
	// 	}

	// 	// Update previous owner ID for next comparison
	// 	previousOwnerId = ticket.owner_id;
	// }

	// React to ticketId changes and select ticket in store
	$: if (ticketId) {
		// Select the ticket in the store - data fetching is handled by tabCacheStore
		ticketStore.selectTicket(ticketId.toString());
	}

	// Additional reactive statement to specifically track ticketId changes
	// $: if (ticketId !== undefined) {
	// 	console.log('ConversationView.svelte: ticketId prop changed:', {
	// 		newTicketId: ticketId,
	// 		timestamp: new Date().toISOString()
	// 	});
	// }

	// Ticket data fetching is now handled by the centralized tabCacheStore
	// No need for manual ticket data fetching in this component

	// Ticket polling is now handled by the centralized tabCacheStore

	// All ticket polling functions removed - now handled by centralized tabCacheStore

	// Set up event listeners for real-time WebSocket message handling
	onMount(() => {
		if (typeof window !== 'undefined') {
			window.addEventListener('platform-new-message', handleNewMessage as EventListener);
			window.addEventListener('platform-batch-complete', handleBatchComplete as EventListener);
		}
	});

	onDestroy(() => {
		isDestroyed = true;
		
		// Cancel any ongoing operations
		if (abortController) {
			abortController.abort();
		}
		
		// Cleanup event listeners
		if (typeof window !== 'undefined') {
			window.removeEventListener('platform-new-message', handleNewMessage as EventListener);
			window.removeEventListener('platform-batch-complete', handleBatchComplete as EventListener);
		}

		// Ticket polling cleanup is now handled by tabCacheStore

		// Cleanup WebSocket connections
		disconnectWebSocket();

		// Clear conversation when component is destroyed
		if (platformId) {
			conversationStore.clearConversation(platformId);
		}
	});

	// async function loadConversationForPlatform(custId: number, platId: number) {
	// 	// Disconnect from previous WebSocket if any
	// 	disconnectWebSocket();

	// 	// Clear previous messages to show loading state
	// 	messages = [];

	// 	try {
	// 		loading = true;

	// 		// Load platform info
	// 		const platformResponse = await fetch(
	// 			`${getBackendUrl()}/customer/api/customers/${custId}/platform-identities/${platId}/`,
	// 			{
	// 				credentials: 'include'
	// 			}
	// 		);

	// 		if (platformResponse.ok) {
	// 			const platformData = await platformResponse.json();

	// 			// Handle both single result and array of results
	// 			const platform = Array.isArray(platformData.results)
	// 				? platformData.results[0]
	// 				: platformData;

	// 			customerName = platform.display_name || platform.platform_username || 'Unknown User';
	// 			channelName = platform.channel_name || platform.platform;
	// 		}

	// 		// Load messages using the store's built-in method
	// 		await conversationStore.loadConversation(custId, platId);

	// 		// Get the loaded messages to check for unread ones
	// 		const loadedMessages = platformMessages;

	// 		// Mark messages as read
	// 		const unreadMessageIds = loadedMessages
	// 			.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
	// 			.map((msg: Message) => msg.id);

	// 		if (unreadMessageIds.length > 0) {
	// 			markMessagesAsRead(unreadMessageIds);
	// 		}

	// 		// Connect WebSocket for this platform
	// 		connectWebSocket(custId, platId);

	// 	} catch (error) {
	// 		console.error('Error loading conversation:', error);
	// 	} finally {
	// 		loading = false;
	// 	}
	// }

	async function loadConversationForPlatform(custId: number, platId: number) {
		console.log('ConversationView: loadConversationForPlatform called:', {
			custId,
			platId,
			activeTab,
			canMarkMessagesAsRead,
			ticketOwnerId: ticket?.owner_id,
			loginUser,
			timestamp: new Date().toISOString()
		});

		// Create new abort controller for this operation
		abortController = new AbortController();

		// Disconnect from previous WebSocket if any
		disconnectWebSocket();

		try {
			loading = true;

			// Check if component is still mounted
			if (isDestroyed) return;

			// Load platform info using service
			if (!access_token) {
				console.error('No access token available');
				return;
			}

			const platformInfo = await services.customers.getPlatformInfo(
				custId,
				platId,
				access_token
			);

			// Check again after async operation
			if (isDestroyed) return;

			if (platformInfo) {

				// Final check before updating state
				if (isDestroyed) return;

				customerName = platformInfo.display_name || platformInfo.platform_username || 'Unknown User';
				channelName = platformInfo.channel_name || platformInfo.platform;

				// Load conversation data
				await conversationStore.loadConversation(custId, platId);

				// Wait a moment for the store to update
				await new Promise(resolve => setTimeout(resolve, 100));

				console.log('ConversationView: Conversation loaded, checking automatic message marking conditions:', {
					isDestroyed,
					canMarkMessagesAsRead,
					activeTab,
					isAssignedTab: (activeTab === 'my-assigned' || activeTab === 'others-assigned'),
					ticketOwnerId: ticket?.owner_id,
					loginUser,
					messagesCount: $conversationStore.messages.get(platId)?.length || 0,
					conversationStoreState: {
						hasMessages: $conversationStore.messages.has(platId),
						messageKeys: Array.from($conversationStore.messages.keys()),
						totalPlatforms: $conversationStore.messages.size
					}
				});

				// Automatic message marking for assigned tabs
				if (!isDestroyed && (activeTab === 'my-assigned' || activeTab === 'others-assigned')) {
					console.log('ConversationView: In assigned tab, attempting automatic message marking...');
					await attemptAutoMarkMessagesAsRead(platId, custId);
					// New logic above, old logic below for comparison

					// Get unread messages from the loaded conversation
					const loadedMessages = $conversationStore.messages.get(platId) || [];
					console.log('ConversationView: Loaded messages:', {
						totalMessages: loadedMessages.length,
						messageDetails: loadedMessages.map(msg => ({
							id: msg.id,
							is_self: msg.is_self,
							status: msg.status,
							message: msg.message?.substring(0, 50) + '...'
						}))
					});

					const unreadMessageIds = loadedMessages
						.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
						.map((msg: Message) => msg.id);

					console.log('ConversationView: Unread customer messages found:', {
						unreadCount: unreadMessageIds.length,
						unreadMessageIds,
						canMarkMessagesAsRead,
						willMarkAsRead: canMarkMessagesAsRead && unreadMessageIds.length > 0
					});

					if (canMarkMessagesAsRead && unreadMessageIds.length > 0) {
						console.log('ConversationView: Marking unread customer messages as read:', unreadMessageIds);
						await markMessagesAsRead(unreadMessageIds);
					} else if (!canMarkMessagesAsRead) {
						console.log('ConversationView: Cannot mark messages as read - user does not own ticket or not in assigned tab');
					} else {
						console.log('ConversationView: No unread customer messages to mark as read');
					}
				} else if (!canMarkMessagesAsRead && (activeTab === 'my-assigned' || activeTab === 'others-assigned')) {
					// If we're in an assigned tab but can't mark messages as read yet (ticket data not loaded),
					// set up a pending request to try again when ticket data becomes available
					console.log('ConversationView: In assigned tab but cannot mark messages as read yet, setting up delayed retry');
					pendingAutoMarkRead = { platformId: platId, customerId: custId };
				} else {
					console.log('ConversationView: Not in assigned tab or component destroyed, skipping automatic message marking');
				}

				// Connect WebSocket only if component is still mounted
				if (!isDestroyed) {
					connectWebSocket(custId, platId);
				}
			}
		} catch (err) {
			if (err.name === 'AbortError') {
				// Operation was cancelled, ignore
				return;
			}
			console.error('Error loading conversation:', err);
			if (!isDestroyed) {
				error = 'Failed to load conversation. Please try again.';
			}
		} finally {
			if (!isDestroyed) {
				loading = false;
			}
		}
	}

	// Handle delayed automatic message marking when ticket data becomes available
	async function handleDelayedAutoMarkRead(platId: number, custId: number) {
		try {
			console.log('ConversationView: handleDelayedAutoMarkRead called:', {
				platId,
				custId,
				canMarkMessagesAsRead,
				activeTab
			});

			if (!canMarkMessagesAsRead || !(activeTab === 'my-assigned' || activeTab === 'others-assigned')) {
				console.log('ConversationView: Conditions no longer met for delayed auto mark read');
				return;
			}

			// Get unread messages from the loaded conversation
			const loadedMessages = $conversationStore.messages.get(platId) || [];
			const unreadMessageIds = loadedMessages
				.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
				.map((msg: Message) => msg.id);

			console.log('ConversationView: Delayed auto mark - found unread messages:', {
				totalMessages: loadedMessages.length,
				unreadCount: unreadMessageIds.length,
				unreadMessageIds
			});

			if (unreadMessageIds.length > 0) {
				console.log('ConversationView: Delayed marking unread customer messages as read:', unreadMessageIds);
				await markMessagesAsRead(unreadMessageIds);
			}
		} catch (error) {
			console.error('ConversationView: Error in handleDelayedAutoMarkRead:', error);
		}
	}

	// Centralized function to attempt automatic message marking
	async function attemptAutoMarkMessagesAsRead(platId: number, custId: number) {
		try {
			console.log('ConversationView: attemptAutoMarkMessagesAsRead called:', {
				platId,
				custId,
				activeTab,
				ticket: ticket ? { owner_id: ticket.owner_id, status: ticket.status } : null,
				loginUser,
				canMarkMessagesAsRead
			});

			// First check: Are we in an assigned tab?
			if (!(activeTab === 'my-assigned' || activeTab === 'others-assigned')) {
				console.log('ConversationView: Not in assigned tab, skipping auto mark');
				return;
			}

			// Second check: Do we have ticket data and does user own the ticket?
			if (!ticket || !ticket.owner_id || ticket.owner_id !== loginUser) {
				console.log('ConversationView: Ticket data not available or user does not own ticket, setting up delayed retry:', {
					hasTicket: !!ticket,
					ticketOwnerId: ticket?.owner_id,
					loginUser,
					willRetry: true
				});
				// Set up delayed retry
				pendingAutoMarkRead = { platformId: platId, customerId: custId };
				return;
			}

			// Third check: Get messages from store
			const loadedMessages = $conversationStore.messages.get(platId) || [];
			console.log('ConversationView: Checking loaded messages:', {
				totalMessages: loadedMessages.length,
				hasMessages: loadedMessages.length > 0,
				storeHasPlatform: $conversationStore.messages.has(platId)
			});

			if (loadedMessages.length === 0) {
				console.log('ConversationView: No messages loaded yet, setting up delayed retry');
				pendingAutoMarkRead = { platformId: platId, customerId: custId };
				return;
			}

			// Fourth check: Find unread customer messages
			const unreadMessageIds = loadedMessages
				.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
				.map((msg: Message) => msg.id);

			console.log('ConversationView: Found unread customer messages:', {
				unreadCount: unreadMessageIds.length,
				unreadMessageIds,
				messageDetails: loadedMessages.map(msg => ({
					id: msg.id,
					is_self: msg.is_self,
					status: msg.status,
					message: msg.message?.substring(0, 30) + '...'
				}))
			});

			// Fifth check: Mark messages as read if any found
			if (unreadMessageIds.length > 0) {
				console.log('ConversationView: Marking unread customer messages as read:', unreadMessageIds);
				await markMessagesAsRead(unreadMessageIds);
			} else {
				console.log('ConversationView: No unread customer messages to mark as read');
			}

		} catch (error) {
			console.error('ConversationView: Error in attemptAutoMarkMessagesAsRead:', error);
		}
	}

	async function loadConversationForTickets(custId: number, platId: number, ticketIds: number[]) {
		try {
			if (!access_token) {
				console.error('No access token available');
				return;
			}

			const result = await customerService.getConversationsForTickets(custId, platId, ticketIds, access_token);

			if (result.res_status === 200 && result.messages.length > 0) {
				// Sort messages by created_on timestamp
				const sortedMessages = result.messages.sort((a: any, b: any) =>
					new Date(a.created_on).getTime() - new Date(b.created_on).getTime()
				);

				// Add messages to store
				conversationStore.setMessages(platId, sortedMessages, result.has_more);
			} else if (result.error_msg) {
				console.error('Failed to load conversation for tickets:', result.error_msg);
			}
		} catch (error) {
			console.error('Error loading conversation for tickets:', error);
		}
	}

	function connectWebSocket(custId: number, platId: number) {
		// For the global platform WebSocket approach
		if (typeof window !== 'undefined') {
			// Subscribe to this specific platform for updates
			platformWebSocket.subscribeToPlatform(platId);
			connected = true;
		}
	}

	function disconnectWebSocket() {
		// Unsubscribe from the current platform if using global WebSocket
		if (platformId && typeof window !== 'undefined') {
			platformWebSocket.unsubscribeFromPlatform(platformId);
		}
		connected = false;
	}

	async function markMessagesAsRead(messageIds: number[]) {
		try {
			console.log('ConversationView: markMessagesAsRead called with:', {
				messageIds,
				customerId,
				platformId,
				access_token: access_token ? 'Present' : 'Missing'
			});

			if (!access_token) {
				console.error('No access token available');
				return;
			}

			const result = await customerService.markMessagesAsRead(customerId, platformId, messageIds, access_token);

			console.log('ConversationView: markMessagesAsRead API response:', result);

			if (result.res_status === 200) {
				console.log('ConversationView: Successfully marked messages as read, updating store...');
				// Update message status in store
				messageIds.forEach((id) => {
					conversationStore.updateMessageStatus(platformId, id, 'READ');
				});
				console.log('ConversationView: Store updated for message IDs:', messageIds);

				// Update unread count in centralized store
				const markedCount = messageIds.length;
				tabCacheStore.decrementUnreadCount(platformId, markedCount);
				console.log('ConversationView: Decremented unread count by', markedCount, 'for platform', platformId);

				// Dispatch event to parent component
				dispatch('messagesMarkedAsRead', {
					platformId,
					customerId,
					messageIds,
					markedCount
				});
			} else {
				console.error('ConversationView: Error marking messages as read:', result.error_msg);
			}
		} catch (error) {
			console.error('ConversationView: Error marking messages as read:', error);
		}
	}

	async function markAllMessagesAsRead() {
		try {
			if (!access_token) {
				console.error('No access token available');
				return;
			}

			const result = await customerService.markAllMessagesAsRead(customerId, platformId, access_token);

			if (result.res_status !== 200) {
				console.error('Error marking all messages as read:', result.error_msg);
			}
		} catch (error) {
			console.error('Error marking messages as read:', error);
		}
	}

	async function handleSendMessage(
		event: CustomEvent<{ content: string; type: string; files?: File[]; preUploadedFiles?: any[] }>
	) {

		// Early return if user cannot send messages
		if (!canSendMessage) {
			// console.log('User is not authorized to send messages for this ticket');
			return;
		}

		const { content, type, files, preUploadedFiles } = event.detail;

		try {
			// Use conversation service for cleaner code
			const response = await conversationService.sendMessage(
				customerId,
				platformId,
				content,
				type,
				files, // Keep for backward compatibility
				preUploadedFiles // New pre-uploaded files parameter
			);

			if (response) {
				// Add message to store
				conversationStore.addMessage(platformId, response);

				// Scroll to bottom after sending message
				// This would be handled in MessageList component
			}
		} catch (error) {
			console.error('Error sending message:', error);
			// Show error notification
			// You could add a toast notification here
		}
	}

	async function handleLoadMore() {
		if (loadingMore || !messages.length || !hasMore) return;

		const oldestMessage = messages[0];
		if (oldestMessage) {
			try {
				// Use the existing loadMoreMessages method which handles loading state internally
				await conversationStore.loadMoreMessages(customerId, platformId, oldestMessage.id);
			} catch (error) {
				console.error('Error loading more messages:', error);
			}
		}
	}

	// // Handle real-time message updates
	// function handleNewMessage(event: CustomEvent) {
	// 	const { platformId: msgPlatformId, message } = event.detail;
	// 	if (msgPlatformId === platformId) {
	// 		// The message is already added to the store by the WebSocket handler

	// 		// Force update if needed
	// 		conversationStore.addMessage(platformId, message);

	// 		// We just need to mark it as read if appropriate
	// 		if (document.hasFocus() && !message.is_self && message.status !== 'READ') {
	// 			markMessagesAsRead([message.id]);
	// 		}
	// 	}
	// }

	// Handle real-time message updates from WebSocket
	function handleNewMessage(event: Event) {
		try {
			// Cast to CustomEvent to access detail property
			const customEvent = event as CustomEvent;
			const { platformId: msgPlatformId, message, updateType, batchId } = customEvent.detail;

			// Only process messages for the currently viewed platform
			if (msgPlatformId === platformId) {
				// Enhanced logging for batch messages
				if (batchId) {
					console.log(`ConversationView: Batch message received - Batch: ${batchId}, Type: ${updateType}, Message: ${message.id}`);
				}

				// Don't add message here - PlatformIdentityList already handles adding to store
				// Use comprehensive validation to determine if message should be auto-marked as read
				console.log('ConversationView: WebSocket message received, evaluating auto-mark conditions:', {
					messageId: message.id,
					messageStatus: message.status,
					isCustomerMessage: !message.is_self,
					canMarkMessagesAsRead,
					access_token: access_token ? 'Present' : 'Missing',
					ticket: ticket ? { owner_id: ticket.owner_id, status: ticket.status } : 'Missing',
					loginUser,
					activeTab
				});

				// Use the comprehensive validation function instead of simple conditions
				if (shouldAutoMarkWebSocketMessage(message)) {
					console.log('ConversationView: Auto-marking WebSocket message as read:', message.id);
					markMessagesAsRead([message.id]);
				} else {
					console.log('ConversationView: Not auto-marking WebSocket message - validation failed');
				}
			}
		} catch (error) {
			console.error('ConversationView: Error handling WebSocket message:', error);
		}
	}

	// Centralized function to determine if a WebSocket message should be auto-marked as read
	function shouldAutoMarkWebSocketMessage(message: any): boolean {
		try {
			console.log('ConversationView: Evaluating shouldAutoMarkWebSocketMessage:', {
				messageId: message.id,
				messageStatus: message.status,
				isCustomerMessage: !message.is_self,
				isInAssignedTab: (activeTab === 'my-assigned' || activeTab === 'others-assigned'),
				hasTicket: !!ticket,
				ticketOwnerId: ticket?.owner_id,
				loginUser,
				userOwnsTicket: ticket?.owner_id === loginUser,
				messageNotAlreadyRead: message.status !== 'READ',
				hasAccessToken: !!access_token
			});

			// 1. Must be a customer message (not self)
			if (message.is_self) {
				console.log('ConversationView: Message is from self, not auto-marking');
				return false;
			}

			// 2. Message must not already be marked as READ
			if (message.status === 'READ') {
				console.log('ConversationView: Message already marked as READ');
				return false;
			}

			// 3. Must be in an assigned tab
			if (!(activeTab === 'my-assigned' || activeTab === 'others-assigned')) {
				console.log('ConversationView: Not in assigned tab, not auto-marking');
				return false;
			}

			// 4. Must have ticket data and user must own the ticket
			if (!ticket || !ticket.owner_id || ticket.owner_id !== loginUser) {
				console.log('ConversationView: User does not own ticket or ticket data missing');
				return false;
			}

			// 5. Must have access token
			if (!access_token) {
				console.log('ConversationView: No access token available');
				return false;
			}

			console.log('ConversationView: All conditions met for auto-marking WebSocket message');
			return true;

		} catch (error) {
			console.error('ConversationView: Error in shouldAutoMarkWebSocketMessage:', error);
			return false;
		}
	}

	// Handle batch completion events from WebSocket
	function handleBatchComplete(event: Event) {
		try {
			const customEvent = event as CustomEvent;
			const { batchId, platformId: eventPlatformId, summary } = customEvent.detail;

			console.log(`ConversationView: Batch ${batchId} completed for platform ${eventPlatformId}:`, summary);

			// The MessageInput component will handle clearing its own loading state
			// This handler is mainly for logging and potential future UI updates
			if (summary.failed > 0) {
				console.warn(`ConversationView: Batch ${batchId} had ${summary.failed} failed messages out of ${summary.total}`);
				// Could show a toast notification here in the future
			}
		} catch (error) {
			console.error('ConversationView: Error handling batch complete event:', error);
		}
	}

	// No longer used
	// $: canSendMessage = latest_ticket_owner_id === currentLoginUser?.id;
	// $: messageInputDisabled = loading || !canSendMessage;

	// console.info('latest_ticket_owner_id', latest_ticket_owner_id);
	// console.info('currentLoginUser', currentLoginUser);
	// console.info('canSendMessage',canSendMessage);
</script>

<div class="flex h-full flex-col">
	<ConversationHeader
		{customerId}
		{customerName}
		{channelName}
		{connected}
		{platformId}
		{users}
		{priorities}
		{statuses}
		{topics}
		{access_token}
		isReadOnly={isReadOnly}
	/>

	<!-- <MessageList 
		{messages}
		{loading}
		on:loadMore={handleLoadMore}
	/> -->

	<MessageList {platformId} {messages} {loading} {loadingMore} {hasMore} {focusedTicketId} on:loadMore={handleLoadMore} />

	{#if showMessageInput} <!-- Disable for monitoring id page -->
		<MessageInput
			on:send={handleSendMessage}
			disabled={messageInputDisabled}
			canSendMessage={canSendMessage}
			isNotTicketOwner={(ticket?.owner_id !== loginUser)}
			isTicketPendingToClose={(ticket?.status.toLowerCase() === 'pending_to_close')}
			isTicketClosed={(ticket?.status.toLowerCase() === 'closed')}
			conversationId={`${customerId}-${platformId}`}
			{customerId}
			{platformId}
		/>
	{/if}
</div>
